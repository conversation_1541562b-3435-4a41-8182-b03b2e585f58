using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace NotificationApp
{
    // Data Models
    public class Notification
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public DateTime AlarmTime { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int SnoozeMinutes { get; set; } = 5;
    }

    // Database Manager
    public class DatabaseManager
    {
        private readonly string connectionString;

        public DatabaseManager(string dbPath = "notifications.db")
        {
            connectionString = $"Data Source={dbPath};Version=3;";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            var createNotificationsTable = @"
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    message TEXT,
                    alarm_time DATETIME NOT NULL,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    snooze_minutes INTEGER NOT NULL DEFAULT 5
                )";

            var createLogTable = @"
                CREATE TABLE IF NOT EXISTS log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    notification_id INTEGER,
                    action TEXT NOT NULL,
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    details TEXT
                )";

            using var command = new SQLiteCommand(createNotificationsTable, connection);
            command.ExecuteNonQuery();

            command.CommandText = createLogTable;
            command.ExecuteNonQuery();
        }

        public List<Notification> GetActiveNotificationsPastDue()
        {
            var notifications = new List<Notification>();
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            var query = @"
                SELECT id, title, message, alarm_time, is_active, created_at, snooze_minutes
                FROM notifications 
                WHERE is_active = 1 AND alarm_time <= @currentTime
                ORDER BY alarm_time";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.Add("@currentTime", System.Data.DbType.DateTime).Value = DateTime.Now;
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                notifications.Add(new Notification
                {
                    Id = reader.GetInt32(0),
                    Title = reader.GetString(1),
                    Message = reader.IsDBNull(2) ? "" : reader.GetString(2),
                    AlarmTime = reader.GetDateTime(3),
                    IsActive = reader.GetBoolean(4),
                    CreatedAt = reader.GetDateTime(5),
                    SnoozeMinutes = reader.GetInt32(6)
                });
            }

            return notifications;
        }

        public List<Notification> GetAllNotifications(bool includeInactive = false)
        {
            var notifications = new List<Notification>();
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            var query = includeInactive 
                ? "SELECT id, title, message, alarm_time, is_active, created_at, snooze_minutes FROM notifications ORDER BY alarm_time DESC"
                : "SELECT id, title, message, alarm_time, is_active, created_at, snooze_minutes FROM notifications WHERE is_active = 1 ORDER BY alarm_time";

            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                notifications.Add(new Notification
                {
                    Id = reader.GetInt32(0),
                    Title = reader.GetString(1),
                    Message = reader.IsDBNull(2) ? "" : reader.GetString(2),
                    AlarmTime = reader.GetDateTime(3),
                    IsActive = reader.GetBoolean(4),
                    CreatedAt = reader.GetDateTime(5),
                    SnoozeMinutes = reader.GetInt32(6)
                });
            }

            return notifications;
        }

        public void MarkNotificationDone(int notificationId)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            var updateQuery = "UPDATE notifications SET is_active = 0 WHERE id = @id";
            using var updateCommand = new SQLiteCommand(updateQuery, connection);
            updateCommand.Parameters.Add("@id", System.Data.DbType.Int32).Value = notificationId;
            updateCommand.ExecuteNonQuery();

            AddLogEntry(notificationId, "DONE", "Notification marked as done", connection);
        }

        public void SnoozeNotification(int notificationId, int minutes)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            var newAlarmTime = DateTime.Now.AddMinutes(minutes);
            var updateQuery = "UPDATE notifications SET alarm_time = @newTime WHERE id = @id";
            using var updateCommand = new SQLiteCommand(updateQuery, connection);
            updateCommand.Parameters.Add("@newTime", System.Data.DbType.DateTime).Value = newAlarmTime;
            updateCommand.Parameters.Add("@id", System.Data.DbType.Int32).Value = notificationId;
            updateCommand.ExecuteNonQuery();

            AddLogEntry(notificationId, "SNOOZE", $"Notification snoozed for {minutes} minutes", connection);
        }

        public void AddNotification(Notification notification)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            var query = @"
                INSERT INTO notifications (title, message, alarm_time, is_active, created_at, snooze_minutes)
                VALUES (@title, @message, @alarmTime, @isActive, @createdAt, @snoozeMinutes)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.Add("@title", System.Data.DbType.String).Value = notification.Title;
            command.Parameters.Add("@message", System.Data.DbType.String).Value = notification.Message;
            command.Parameters.Add("@alarmTime", System.Data.DbType.DateTime).Value = notification.AlarmTime;
            command.Parameters.Add("@isActive", System.Data.DbType.Boolean).Value = notification.IsActive;
            command.Parameters.Add("@createdAt", System.Data.DbType.DateTime).Value = DateTime.Now;
            command.Parameters.Add("@snoozeMinutes", System.Data.DbType.Int32).Value = notification.SnoozeMinutes;
            command.ExecuteNonQuery();
        }

        private void AddLogEntry(int notificationId, string action, string details, SQLiteConnection connection)
        {
            var logQuery = @"
                INSERT INTO log (notification_id, action, timestamp, details)
                VALUES (@notificationId, @action, @timestamp, @details)";

            using var logCommand = new SQLiteCommand(logQuery, connection);
            logCommand.Parameters.Add("@notificationId", System.Data.DbType.Int32).Value = notificationId;
            logCommand.Parameters.Add("@action", System.Data.DbType.String).Value = action;
            logCommand.Parameters.Add("@timestamp", System.Data.DbType.DateTime).Value = DateTime.Now;
            logCommand.Parameters.Add("@details", System.Data.DbType.String).Value = details;
            logCommand.ExecuteNonQuery();
        }
    }

    // Main Application
    public partial class App : System.Windows.Application
    {
        private DatabaseManager dbManager;
        private System.Timers.Timer checkTimer;
        private MainWindow mainWindow;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            dbManager = new DatabaseManager();
            mainWindow = new MainWindow(dbManager);
            mainWindow.Show();
            MainWindow = mainWindow;

            // Setup timer
            checkTimer = new System.Timers.Timer(30000);
            checkTimer.Elapsed += CheckForNotifications;
            checkTimer.Start();
            
            CheckForNotifications(null, null);
        }

        private void CheckForNotifications(object sender, ElapsedEventArgs e)
        {
            var notifications = dbManager.GetActiveNotificationsPastDue();
            if (notifications.Count > 0)
            {
                Dispatcher.Invoke(() => ShowNotificationWindow(notifications));
            }
        }

        private void ShowNotificationWindow(List<Notification> notifications)
        {
            var notificationWindow = new NotificationWindow(notifications, dbManager);
            notificationWindow.Show();
            
            if (mainWindow.WindowState == WindowState.Minimized)
                mainWindow.WindowState = WindowState.Normal;
            mainWindow.Activate();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            checkTimer?.Dispose();
            base.OnExit(e);
        }
    }

    // Notification Display Window
    public partial class NotificationWindow : Window
    {
        private readonly List<Notification> notifications;
        private readonly DatabaseManager dbManager;
        private Storyboard blinkStoryboard;

        public NotificationWindow(List<Notification> notifications, DatabaseManager dbManager)
        {
            this.notifications = notifications;
            this.dbManager = dbManager;
            InitializeComponent();
            SetupBlinkingBorder();
        }

        private void InitializeComponent()
        {
            Title = "Active Notifications";
            Width = 500;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Topmost = true;

            var border = new Border
            {
                BorderBrush = Brushes.Red,
                BorderThickness = new Thickness(3),
                CornerRadius = new CornerRadius(5)
            };

            var mainPanel = new StackPanel { Margin = new Thickness(10) };
            
            var titleLabel = new Label
            {
                Content = $"You have {notifications.Count} active notification(s):",
                FontWeight = FontWeights.Bold,
                FontSize = 14
            };
            mainPanel.Children.Add(titleLabel);

            var scrollViewer = new ScrollViewer
            {
                MaxHeight = 250,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto
            };

            var notificationPanel = new StackPanel();

            foreach (var notification in notifications)
            {
                var notifBorder = new Border
                {
                    BorderBrush = Brushes.LightGray,
                    BorderThickness = new Thickness(1),
                    Margin = new Thickness(0, 5, 0, 5),
                    Padding = new Thickness(10),
                    Background = Brushes.LightYellow
                };

                var notifPanel = new StackPanel();
                
                notifPanel.Children.Add(new TextBlock
                {
                    Text = notification.Title,
                    FontWeight = FontWeights.Bold,
                    FontSize = 12
                });

                if (!string.IsNullOrEmpty(notification.Message))
                {
                    notifPanel.Children.Add(new TextBlock
                    {
                        Text = notification.Message,
                        TextWrapping = TextWrapping.Wrap,
                        Margin = new Thickness(0, 5, 0, 5)
                    });
                }

                notifPanel.Children.Add(new TextBlock
                {
                    Text = $"Alarm: {notification.AlarmTime:yyyy-MM-dd HH:mm}",
                    FontSize = 10,
                    Foreground = Brushes.Gray
                });

                var buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 10, 0, 0)
                };

                var doneButton = new Button
                {
                    Content = "Done",
                    Margin = new Thickness(0, 0, 10, 0),
                    Padding = new Thickness(15, 5, 15, 5),
                    Background = Brushes.LightGreen
                };
                doneButton.Click += (s, e) => MarkDone(notification.Id);

                var snoozeButton = new Button
                {
                    Content = $"Snooze ({notification.SnoozeMinutes}m)",
                    Padding = new Thickness(15, 5, 15, 5),
                    Background = Brushes.LightBlue
                };
                snoozeButton.Click += (s, e) => SnoozeNotification(notification.Id, notification.SnoozeMinutes);

                buttonPanel.Children.Add(doneButton);
                buttonPanel.Children.Add(snoozeButton);
                notifPanel.Children.Add(buttonPanel);

                notifBorder.Child = notifPanel;
                notificationPanel.Children.Add(notifBorder);
            }

            scrollViewer.Content = notificationPanel;
            mainPanel.Children.Add(scrollViewer);

            border.Child = mainPanel;
            Content = border;
        }

        private void SetupBlinkingBorder()
        {
            var border = (Border)Content;
            blinkStoryboard = new Storyboard();
            blinkStoryboard.RepeatBehavior = RepeatBehavior.Forever;

            var colorAnimation = new ColorAnimation
            {
                From = Colors.Red,
                To = Colors.Transparent,
                Duration = TimeSpan.FromMilliseconds(500),
                AutoReverse = true
            };

            Storyboard.SetTarget(colorAnimation, border);
            Storyboard.SetTargetProperty(colorAnimation, new PropertyPath("BorderBrush.Color"));
            
            blinkStoryboard.Children.Add(colorAnimation);
            blinkStoryboard.Begin();
        }

        private void MarkDone(int notificationId)
        {
            dbManager.MarkNotificationDone(notificationId);
            RefreshWindow();
        }

        private void SnoozeNotification(int notificationId, int minutes)
        {
            dbManager.SnoozeNotification(notificationId, minutes);
            RefreshWindow();
        }

        private void RefreshWindow()
        {
            var remainingNotifications = dbManager.GetActiveNotificationsPastDue();
            if (remainingNotifications.Count == 0)
            {
                blinkStoryboard?.Stop();
                Close();
            }
            else
            {
                blinkStoryboard?.Stop();
                Close();
                var newWindow = new NotificationWindow(remainingNotifications, dbManager);
                newWindow.Show();
            }
        }
    }

    // Main Management Window
    public partial class MainWindow : Window
    {
        private DatabaseManager dbManager;
        private DataGrid notificationGrid;
        private System.Windows.Controls.ComboBox viewFilter;
        private List<Notification> currentNotifications;

        public MainWindow(DatabaseManager dbManager)
        {
            this.dbManager = dbManager;
            currentNotifications = new List<Notification>();
            InitializeComponent();
            LoadNotifications();
        }

        private void InitializeComponent()
        {
            Title = "Notification Manager";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            var mainPanel = new DockPanel();

            // Toolbar
            var toolbar = new ToolBar();
            DockPanel.SetDock(toolbar, Dock.Top);

            viewFilter = new System.Windows.Controls.ComboBox
            {
                Width = 150,
                Margin = new Thickness(5)
            };
            viewFilter.Items.Add(new ComboBoxItem { Content = "Active Only", IsSelected = true });
            viewFilter.Items.Add(new ComboBoxItem { Content = "All Notifications" });
            viewFilter.SelectionChanged += ViewFilter_SelectionChanged;

            var newButton = new Button
            {
                Content = "New Notification",
                Margin = new Thickness(5),
                Padding = new Thickness(10, 5, 10, 5)
            };
            newButton.Click += NewButton_Click;

            var refreshButton = new Button
            {
                Content = "Refresh",
                Margin = new Thickness(5),
                Padding = new Thickness(10, 5, 10, 5)
            };
            refreshButton.Click += (s, e) => LoadNotifications();

            var minimizeButton = new Button
            {
                Content = "Minimize",
                Margin = new Thickness(5),
                Padding = new Thickness(10, 5, 10, 5)
            };
            minimizeButton.Click += (s, e) => WindowState = WindowState.Minimized;

            toolbar.Items.Add(new Label { Content = "View:" });
            toolbar.Items.Add(viewFilter);
            toolbar.Items.Add(new Separator());
            toolbar.Items.Add(newButton);
            toolbar.Items.Add(refreshButton);
            toolbar.Items.Add(new Separator());
            toolbar.Items.Add(minimizeButton);

            mainPanel.Children.Add(toolbar);

            // Data Grid
            notificationGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                SelectionMode = DataGridSelectionMode.Single
            };

            notificationGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "ID",
                Binding = new Binding("Id"),
                Width = 50
            });

            notificationGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "Title",
                Binding = new Binding("Title"),
                Width = 200
            });

            notificationGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "Message",
                Binding = new Binding("Message"),
                Width = 250
            });

            notificationGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "Alarm Time",
                Binding = new Binding("AlarmTime"),
                Width = 150
            });

            notificationGrid.Columns.Add(new DataGridCheckBoxColumn
            {
                Header = "Active",
                Binding = new Binding("IsActive"),
                Width = 60
            });

            mainPanel.Children.Add(notificationGrid);
            Content = mainPanel;
        }

        private void LoadNotifications()
        {
            bool includeInactive = viewFilter.SelectedIndex == 1;
            currentNotifications = dbManager.GetAllNotifications(includeInactive);
            notificationGrid.ItemsSource = currentNotifications;
        }

        private void ViewFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            LoadNotifications();
        }

        private void NewButton_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new EditNotificationWindow(dbManager);
            if (editWindow.ShowDialog() == true)
            {
                LoadNotifications();
            }
        }
    }

    // Edit/Create Notification Window
    public partial class EditNotificationWindow : Window
    {
        private DatabaseManager dbManager;
        private Notification notification;
        private System.Windows.Controls.TextBox titleTextBox;
        private System.Windows.Controls.TextBox messageTextBox;
        private DatePicker datePicker;
        private System.Windows.Controls.TextBox timeTextBox;
        private System.Windows.Controls.TextBox snoozeTextBox;
        private System.Windows.Controls.CheckBox activeCheckBox;

        public EditNotificationWindow(DatabaseManager dbManager, Notification notification = null)
        {
            this.dbManager = dbManager;
            this.notification = notification;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            Title = notification == null ? "New Notification" : "Edit Notification";
            Width = 400;
            Height = 350;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            ResizeMode = ResizeMode.NoResize;

            var mainPanel = new StackPanel { Margin = new Thickness(20) };

            mainPanel.Children.Add(new Label { Content = "Title:" });
            titleTextBox = new System.Windows.Controls.TextBox { Margin = new Thickness(0, 0, 0, 10) };
            mainPanel.Children.Add(titleTextBox);

            mainPanel.Children.Add(new Label { Content = "Message:" });
            messageTextBox = new System.Windows.Controls.TextBox 
            { 
                Height = 60, 
                TextWrapping = TextWrapping.Wrap,
                AcceptsReturn = true,
                Margin = new Thickness(0, 0, 0, 10)
            };
            mainPanel.Children.Add(messageTextBox);

            mainPanel.Children.Add(new Label { Content = "Date:" });
            datePicker = new DatePicker { Margin = new Thickness(0, 0, 0, 10) };
            mainPanel.Children.Add(datePicker);

            mainPanel.Children.Add(new Label { Content = "Time (HH:MM):" });
            timeTextBox = new System.Windows.Controls.TextBox { Margin = new Thickness(0, 0, 0, 10) };
            mainPanel.Children.Add(timeTextBox);

            mainPanel.Children.Add(new Label { Content = "Snooze Minutes:" });
            snoozeTextBox = new System.Windows.Controls.TextBox { Margin = new Thickness(0, 0, 0, 10) };
            mainPanel.Children.Add(snoozeTextBox);

            activeCheckBox = new System.Windows.Controls.CheckBox 
            { 
                Content = "Active",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 20)
            };
            mainPanel.Children.Add(activeCheckBox);

            var buttonPanel = new StackPanel 
            { 
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var saveButton = new Button
            {
                Content = "Save",
                Width = 75,
                Margin = new Thickness(0, 0, 10, 0),
                IsDefault = true
            };
            saveButton.Click += SaveButton_Click;

            var cancelButton = new Button
            {
                Content = "Cancel",
                Width = 75,
                IsCancel = true
            };

            buttonPanel.Children.Add(saveButton);
            buttonPanel.Children.Add(cancelButton);
            mainPanel.Children.Add(buttonPanel);

            Content = mainPanel;
        }

        private void LoadData()
        {
            if (notification != null)
            {
                titleTextBox.Text = notification.Title;
                messageTextBox.Text = notification.Message;
                datePicker.SelectedDate = notification.AlarmTime.Date;
                timeTextBox.Text = notification.AlarmTime.ToString("HH:mm");
                snoozeTextBox.Text = notification.SnoozeMinutes.ToString();
                activeCheckBox.IsChecked = notification.IsActive;
            }
            else
            {
                datePicker.SelectedDate = DateTime.Today;
                timeTextBox.Text = DateTime.Now.AddHours(1).ToString("HH:mm");
                snoozeTextBox.Text = "5";
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                var alarmDateTime = datePicker.SelectedDate.Value.Date;
                if (TimeSpan.TryParse(timeTextBox.Text, out var timeSpan))
                {
                    alarmDateTime = alarmDateTime.Add(timeSpan);
                }

                if (notification == null)
                {
                    var newNotification = new Notification
                    {
                        Title = titleTextBox.Text,
                        Message = messageTextBox.Text,
                        AlarmTime = alarmDateTime,
                        IsActive = activeCheckBox.IsChecked == true,
                        SnoozeMinutes = int.Parse(snoozeTextBox.Text)
                    };
                    dbManager.AddNotification(newNotification);
                }

                DialogResult = true;
                Close();
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(titleTextBox.Text))
            {
                MessageBox.Show("Title is required.", "Validation Error");
                return false;
            }

            if (!datePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("Date is required.", "Validation Error");
                return false;
            }

            if (!TimeSpan.TryParse(timeTextBox.Text, out _))
            {
                MessageBox.Show("Invalid time format. Use HH:MM format.", "Validation Error");
                return false;
            }

            if (!int.TryParse(snoozeTextBox.Text, out int snoozeMinutes) || snoozeMinutes <= 0)
            {
                MessageBox.Show("Snooze minutes must be a positive number.", "Validation Error");
                return false;
            }

            return true;
        }
    }
}